package com.cjx.ollama.template;


/**
 * 会话导出服务的抽象类，负责定义导出流程的固定部分。
 *      * --------------------------------------------------模板方法设计模式--------------------------------------------------
 *      * 模板方法：processExport方法定义了导出流程的 “骨架”（先验证、再处理），固定了步骤顺序
 *      * 钩子方法：ContentProcessor.process作为 “钩子”，允许子类（这里通过 Lambda 实现）自定义流程中的特定步骤（如不同格式的内容生成）
 *      * 处理会话导出的通用方法，封装会话验证和文件内容生成逻辑

 */
public abstract class AbstractExportService {


}
