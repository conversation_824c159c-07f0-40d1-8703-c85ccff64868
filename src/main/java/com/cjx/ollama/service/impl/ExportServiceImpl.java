package com.cjx.ollama.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cjx.ollama.exception.CustomerException;
import com.cjx.ollama.mapper.ChatSessionMapper;
import com.cjx.ollama.pojo.entity.ChatMessage;
import com.cjx.ollama.pojo.entity.ChatSession;
import com.cjx.ollama.pojo.info.SessionInfo;
import com.cjx.ollama.result.ResultEnum;
import com.cjx.ollama.service.ChatMessageService;
import com.cjx.ollama.service.ExportService;
import com.cjx.ollama.component.ImageLoader;
import com.cjx.ollama.utils.session.export.*;
import com.cjx.ollama.utils.session.export.image.ImageUtil;
import com.cjx.ollama.utils.session.export.link.LinkUtil;
import com.cjx.ollama.utils.session.SessionUtil;
import com.cjx.ollama.utils.verify.ValidationUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import java.io.StringWriter;
import java.util.List;
import java.util.stream.Collectors;
import static com.cjx.ollama.utils.constant.Export.DEFAULT_IMAGE_CATEGORY;
import static com.cjx.ollama.utils.constant.Export.DEFAULT_POST_CATEGORY;


/**
 * 会话导出服务实现类
 * 支持TXT/HTML/XML/Excel/CSV多种格式导出，区分文章分类和图片分类
 * author cjx
 * date 2025/7/18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ExportServiceImpl extends ServiceImpl<ChatSessionMapper, ChatSession> implements ExportService {

    private final LinkUtil linkUtil;
    private final ImageUtil imageUtil;
    private final ImageLoader imageLoader;
    private final ChatMessageService chatMessageService;

    /**
     * 导出会话标题列表为TXT
     *
     * @param userId 用户ID
     * @return 会话标题字符串（换行分隔）
     */
    @Override
    public String exportSessionNameTxt(Long userId) {
        // 参数校验
        ValidationUtil.validateUserId(userId);
        log.info("开始导出会话标题列表，userId: {}", userId);

        List<ChatSession> sessions = this.lambdaQuery()
                .select(ChatSession::getSessionName)
                .eq(ChatSession::getUserId, userId)
                .list();

        if (sessions.isEmpty()) {
            log.warn("导出会话标题列表失败：未找到用户会话，userId: {}", userId);
            throw new CustomerException(ResultEnum.SESSION_NOT_FOUND);
        }

        // 拼接会话名称
        String sessionNames = sessions.stream()
                .map(ChatSession::getSessionName)
                .collect(Collectors.joining("\n"));

        // 写入文件
        FileWriteUtil.writeSessionNameTxt(sessionNames, sessions.size());

        // 直接记录成功日志（无LogUtil）
        log.info("会话标题TXT 导出成功，userId: {}, 导出会话数量: {}", userId, sessions.size());
        return sessionNames;
    }

    /**
     * 导出会话为HTML（含图片分类处理）
     *
     * @param userId        用户ID
     * @param sessionId     会话ID
     * @param imageCategory 图片分类
     * @return HTML内容
     */
    @Override
    public String exportSessionHtml(Long userId, String sessionId, String imageCategory) {
        // 处理图片分类默认值
        String processedImageCategory = CategoryUtil.getProcessedCategory(imageCategory, DEFAULT_IMAGE_CATEGORY);

        // 参数校验
        ValidationUtil.validateUserId(userId);
        ValidationUtil.validateSessionId(sessionId);
        ValidationUtil.validateImageCategory(imageCategory);
        log.info("开始导出会话为HTML，sessionId: {}, imageCategory: {}", sessionId, imageCategory);

        return processExport(
                userId,
                sessionId,
                (session, messages) -> {
            // 生成基础HTML
            String html = MarkdownUtils.generateSessionHtml(session, messages);
            // 添加超链接
            String htmlWithLink = linkUtil.insertLinks(html);
            // 插入图片链接
            ImageUtil.ImageInjectionResult result = imageUtil.insertImageLink(
                    htmlWithLink,
                    imageLoader.getImagesByCategory(processedImageCategory)
            );
            // 写入文件
            FileWriteUtil.writeSessionHtml(session, result.htmlContent());

            // 直接记录成功日志
            log.info("HTML 导出成功，userId: {}, sessionId: {}, 消息数量: {}",
                    userId, sessionId, messages.size());
            return result.htmlContent();
        });
    }

    /**
     * 导出会话为WordPress XML（区分文章分类和图片分类）
     *
     * @param userId        用户ID
     * @param sessionId     会话ID
     * @param postCategory  文章分类
     * @param imageCategory 图片分类
     * @return XML内容
     */
    @Override
    public String exportSessionXml(Long userId, String sessionId, String postCategory, String imageCategory) {
        // 参数校验
        ValidationUtil.validateUserId(userId);
        ValidationUtil.validateSessionId(sessionId);
        ValidationUtil.validatePostCategory(postCategory);
        ValidationUtil.validateImageCategory(imageCategory);
        log.info("开始导出会话为XML，sessionId: {}, postCategory: {}, imageCategory: {}",
                sessionId, postCategory, imageCategory);

        // 处理分类默认值
        String processedPostCategory = CategoryUtil.getProcessedCategory(postCategory, DEFAULT_POST_CATEGORY);
        String processedImageCategory = CategoryUtil.getProcessedCategory(imageCategory, DEFAULT_IMAGE_CATEGORY);

        return processExport(
                userId,
                sessionId,
                (session, messages) -> {
            // 处理消息内容
            StringBuilder contentBuilder = new StringBuilder();
            for (ChatMessage msg : messages) {
                String htmlContent = MarkdownUtils.processMessageContent(msg.getContent());
                String htmlWithLink = linkUtil.insertLinks(htmlContent);
                ImageUtil.ImageInjectionResult imageResult = imageUtil.insertImageLink(
                        htmlWithLink,
                        imageLoader.getImagesByCategory(processedImageCategory)
                );
                contentBuilder.append(imageResult.htmlContent()).append("\n");
            }

            // 生成XML
            String xml = WordPressXmlUtil.generateWordPressXml(
                    session,
                    session.getSessionName(),
                    contentBuilder.toString(),
                    processedPostCategory
            );

            // 写入文件
            FileWriteUtil.writeSessionXml(session, xml);

            // 直接记录成功日志
            log.info("XML 导出成功，userId: {}, sessionId: {}, 消息数量: {}",
                    userId, sessionId, messages.size());
            return xml;
        });
    }

    /**
     * 导出会话为Excel（TSV文本格式）
     *
     * @param userId        用户ID
     * @param sessionId     会话ID
     * @param postCategory  文章分类
     * @param imageCategory 图片分类
     * @return Excel文本内容
     */
    @Override
    public String exportSessionExcel(Long userId, String sessionId, String postCategory, String imageCategory) {
        // 参数校验
        ValidationUtil.validateUserId(userId);
        ValidationUtil.validateSessionId(sessionId);
        ValidationUtil.validatePostCategory(postCategory);
        ValidationUtil.validateImageCategory(imageCategory);
        log.info("开始导出会话为Excel，sessionId: {}, postCategory: {}, imageCategory: {}",
                sessionId, postCategory, imageCategory);

        // 处理分类默认值
        String processedPostCategory = CategoryUtil.getProcessedCategory(postCategory, DEFAULT_POST_CATEGORY);
        String processedImageCategory = CategoryUtil.getProcessedCategory(imageCategory, DEFAULT_IMAGE_CATEGORY);

        return processExport(
                userId,
                sessionId,
                (session, messages) -> {
            try (StringWriter stringWriter = new StringWriter()) {
                // 写入UTF-8 BOM
                stringWriter.write('\uFEFF');
                // 表头
                stringWriter.write("序号\t会话标题\t消息内容（HTML）\t文章分类\t图片链接\t关联链接\n");

                // 填充数据
                for (int i = 0; i < messages.size(); i++) {
                    ChatMessage msg = messages.get(i);
                    String htmlContent = MarkdownUtils.toHtml(msg.getContent());
                    String htmlWithLink = linkUtil.insertLinks(htmlContent);
                    ImageUtil.ImageInjectionResult imageResult = imageUtil.insertImageLink(
                            htmlWithLink,
                            imageLoader.getImagesByCategory(processedImageCategory)
                    );

                    // 构建行数据
                    String line = String.format("%d\t%s\t%s\t%s\t%s\t%s%n",
                            i + 1,
                            EscapeUtil.escapeTabSeparated(session.getSessionName()),
                            EscapeUtil.escapeTabSeparated(imageResult.htmlContent()),
                            EscapeUtil.escapeTabSeparated(processedPostCategory),
                            EscapeUtil.escapeTabSeparated(imageResult.imageUrl()),
                            ""
                    );
                    stringWriter.write(line);
                }

                String excelTextContent = stringWriter.toString();
                // 写入文件
                FileWriteUtil.writeSessionExcel(session, excelTextContent);

                // 直接记录成功日志
                log.info("Excel 导出成功，userId: {}, sessionId: {}, 消息数量: {}, 内容类型: 文本内容",
                        userId, sessionId, messages.size());
                return excelTextContent;

            } catch (Exception e) {
                log.error("Excel生成失败，sessionId: {}", sessionId, e);
                throw new CustomerException(ResultEnum.FILE_WRITE_ERROR);
            }
        });
    }

    /**
     * 导出会话为CSV（纯文本格式）
     *
     * @param userId        用户ID
     * @param sessionId     会话ID
     * @param postCategory  文章分类
     * @param imageCategory 图片分类
     * @return CSV内容字符串
     */
    @Override
    public String exportSessionCsv(Long userId, String sessionId, String postCategory, String imageCategory) {
        // 参数校验
        ValidationUtil.validateUserId(userId);
        ValidationUtil.validateSessionId(sessionId);
        ValidationUtil.validatePostCategory(postCategory);
        ValidationUtil.validateImageCategory(imageCategory);
        log.info("开始导出会话为CSV，sessionId: {}, postCategory: {}, imageCategory: {}",
                sessionId, postCategory, imageCategory);

        // 处理分类默认值
        String processedPostCategory = CategoryUtil.getProcessedCategory(postCategory, DEFAULT_POST_CATEGORY);
        String processedImageCategory = CategoryUtil.getProcessedCategory(imageCategory, DEFAULT_IMAGE_CATEGORY);

        return processExport(
                userId,
                sessionId,
                (session, messages) -> {
            try (StringWriter stringWriter = new StringWriter()) {
                // 写入UTF-8 BOM
                stringWriter.write('\uFEFF');
                // 表头
                stringWriter.write("序号,会话标题,消息内容（HTML）,文章分类,图片链接,关联链接\n");

                // 填充数据
                for (int i = 0; i < messages.size(); i++) {
                    ChatMessage msg = messages.get(i);
                    String htmlContent = MarkdownUtils.toHtml(msg.getContent());
                    String htmlWithLink = linkUtil.insertLinks(htmlContent);
                    ImageUtil.ImageInjectionResult imageResult = imageUtil.insertImageLink(
                            htmlWithLink,
                            imageLoader.getImagesByCategory(processedImageCategory)
                    );

                    // 构建行数据
                    String line = String.format("%d,%s,%s,%s,%s,%s%n",
                            i + 1,
                            EscapeUtil.escapeCsv(session.getSessionName()),
                            EscapeUtil.escapeCsv(imageResult.htmlContent()),
                            EscapeUtil.escapeCsv(processedPostCategory),
                            EscapeUtil.escapeCsv(imageResult.imageUrl()),
                            ""
                    );
                    stringWriter.write(line);
                }

                String csvContent = stringWriter.toString();
                // 写入文件
                FileWriteUtil.writeSessionCsv(session, csvContent);

                // 直接记录成功日志
                log.info("CSV 导出成功，userId: {}, sessionId: {}, 消息数量: {}, 内容类型: CSV内容字符串",
                        userId, sessionId, messages.size());
                return csvContent;

            } catch (Exception e) {
                log.error("CSV生成失败，sessionId: {}", sessionId, e);
                throw new CustomerException(ResultEnum.FILE_WRITE_ERROR);
            }
        });
    }



    /**
     * --------------------------------------------------模板方法设计模式--------------------------------------------------
     * 模板方法：processExport方法定义了导出流程的 “骨架”（先验证、再处理），固定了步骤顺序
     * 钩子方法：ContentProcessor.process作为 “钩子”，允许子类（这里通过 Lambda 实现）自定义流程中的特定步骤（如不同格式的内容生成）
     * 处理会话导出的通用方法，封装会话验证和文件内容生成逻辑
     */

    //定义导出流程的 “骨架”
    private <T> T processExport(Long userId, String sessionId, ContentProcessor<T> processor) {
        //固定步骤
        // 1.获取验证后的会话和消息
        SessionInfo sessionInfo = SessionUtil.getValidSessionAndMessages(
                userId,
                sessionId,
                this,
                chatMessageService
        );
        // 2.调用处理器生成内容
        return processor.process(sessionInfo.session(), sessionInfo.messages());
    }

    /**
     * 内容处理器接口，定义不同格式文件的内容生成逻辑
     */
    //钩子方法：作为可变部分，允许不同的实现
    //自定义流程中的特定步骤：（对会话和消息进行自定义处理）
    @FunctionalInterface //标记一个接口为函数式接口，即该接口只能有一个抽象方法（可以有默认方法或静态方法）
    private interface ContentProcessor<T> {
        T process(ChatSession session, List<ChatMessage> messages);
    }
}